using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إنشاء ملفات PDF لتقارير التكليف
    /// </summary>
    public class AssignmentPdfService
    {
        /// <summary>
        /// إنشاء ملف PDF لتقرير التكليف
        /// </summary>
        public async Task GenerateAssignmentPdfAsync(FieldVisit visit, string filePath = null)
        {
            try
            {
                // تكوين QuestPDF
                QuestPDF.Settings.License = LicenseType.Community;

                // جلب البيانات المطلوبة
                var data = await GetAssignmentDataAsync(visit);

                // إنشاء المستند
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(20);
                        page.DefaultTextStyle(x => x.FontFamily("Arial"));

                        page.Content().Column(column =>
                        {
                            // الهيدر
                            CreateHeader(column.Item(), data);

                            column.Item().PaddingVertical(10);

                            // تفاصيل المشروع
                            CreateProjectDetails(column.Item(), data);

                            column.Item().PaddingVertical(5);

                            // النشاط
                            CreateActivitySection(column.Item(), visit);

                            column.Item().PaddingVertical(5);

                            // خط السير
                            CreateItinerary(column.Item(), visit);

                            column.Item().PaddingVertical(5);

                            // تاريخ التحرك
                            CreateMovementDate(column.Item(), visit);

                            column.Item().PaddingVertical(10);

                            // جدول المشاركين
                            CreateParticipantsTable(column.Item(), visit);

                            column.Item().PaddingVertical(10);

                            // جدول النقل
                            CreateTransportTable(column.Item(), visit);

                            column.Item().PaddingVertical(10);

                            // التوقيعات
                            CreateSignatures(column.Item());
                        });

                        // الفوتر
                        page.Footer().AlignCenter().Text("الصندوق الاجتماعي للتنمية - فرع ذمار والبيضاء")
                            .FontSize(10).DirectionFromRightToLeft();
                    });
                });

                // حفظ الملف
                string finalFilePath;
                if (string.IsNullOrEmpty(filePath))
                {
                    var fileName = $"تكليف_الزيارة_{visit.VisitNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                    var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                    finalFilePath = Path.Combine(desktopPath, fileName);
                }
                else
                {
                    finalFilePath = filePath;
                }

                await Task.Run(() => document.GeneratePdf(finalFilePath));

                // فتح الملف
                Process.Start(new ProcessStartInfo(finalFilePath) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف PDF: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// جلب بيانات التكليف
        /// </summary>
        private async Task<AssignmentData> GetAssignmentDataAsync(FieldVisit visit)
        {
            using var context = new ApplicationDbContext();
            
            var projects = await context.FieldVisitProjects
                .Where(fvp => fvp.FieldVisitId == visit.Id)
                .Select(fvp => new ProjectInfo
                {
                    ProjectNumber = fvp.ProjectNumber,
                    ProjectName = fvp.ProjectName
                })
                .ToListAsync();

            return new AssignmentData
            {
                Visit = visit,
                Projects = projects
            };
        }

        /// <summary>
        /// إنشاء الهيدر
        /// </summary>
        private void CreateHeader(IContainer container, AssignmentData data)
        {
            container.Column(column =>
            {
                // الصف الأول - الشعار والمؤسسة
                column.Item().Row(row =>
                {
                    // الشعار (يسار)
                    row.ConstantItem(80).Column(col =>
                    {
                        var logoPath = GetLogoPath();
                        if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                        {
                            col.Item().Image(logoPath).FitArea();
                        }
                    });

                    // مساحة فاصلة
                    row.ConstantItem(20);

                    // المؤسسة (يمين)
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().PaddingTop(5).AlignRight().Text("الصندوق الاجتماعي للتنمية")
                            .FontSize(16).Bold().DirectionFromRightToLeft();
                        col.Item().PaddingTop(8).AlignRight().Text("فرع ذمار والبيضاء")
                            .FontSize(14).DirectionFromRightToLeft();
                    });
                });

                column.Item().PaddingVertical(10);

                // العنوان الرئيسي
                column.Item()
                    .Border(2)
                    .BorderColor("#000000")
                    .Padding(10)
                    .AlignCenter()
                    .Text("تكليف مهمة ميدانية")
                    .FontSize(18)
                    .Bold()
                    .DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// الحصول على مسار الشعار
        /// </summary>
        private string GetLogoPath()
        {
            var possiblePaths = new[]
            {
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icons", "sfd.png"),
                Path.Combine(Directory.GetCurrentDirectory(), "Icons", "sfd.png"),
                Path.Combine(Environment.CurrentDirectory, "Icons", "sfd.png"),
                "Icons/sfd.png",
                "sfd.png"
            };

            return possiblePaths.FirstOrDefault(File.Exists);
        }

        /// <summary>
        /// إنشاء تفاصيل المشروع
        /// </summary>
        private void CreateProjectDetails(IContainer container, AssignmentData data)
        {
            container.Column(column =>
            {
                var projectText = data.Projects.Any()
                    ? string.Join(" - ", data.Projects.Select(p => $"{p.ProjectNumber} - {p.ProjectName}"))
                    : "تأهيل وتحسين طريق وحصاد مياه بقرية ظلمان ـ عزلة الرعية ـ المنار ـ ذمار";

                column.Item().AlignRight().Text($"{projectText} : المشروع •")
                    .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// إنشاء قسم النشاط
        /// </summary>
        private void CreateActivitySection(IContainer container, FieldVisit visit)
        {
            var activityText = !string.IsNullOrEmpty(visit.MissionPurpose)
                ? visit.MissionPurpose
                : "المتابعه المجتمعية وحصر الاسر الغير عاملة والغير متواجدة";

            container.AlignRight().Text($"{activityText} : النشــاط •")
                .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
        }

        /// <summary>
        /// إنشاء خط السير
        /// </summary>
        private void CreateItinerary(IContainer container, FieldVisit visit)
        {
            var itineraryText = visit.Itinerary != null && visit.Itinerary.Any()
                ? string.Join(" - ", visit.Itinerary)
                : "ذمار معبر ضوران المنار قرية ظلمان عزلة الرعية";

            container.AlignRight().Text($"خط السير : {itineraryText} •")
                .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
        }

        /// <summary>
        /// إنشاء تاريخ التحرك
        /// </summary>
        private void CreateMovementDate(IContainer container, FieldVisit visit)
        {
            container.AlignRight().Text($"تاريخ التحرك : مــن {visit.DepartureDate:dd/MM/yyyy} الى {visit.ReturnDate:dd/MM/yyyy} •")
                .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
        }

        /// <summary>
        /// إنشاء جدول المشاركين
        /// </summary>
        private void CreateParticipantsTable(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                // عنوان الجدول
                column.Item().AlignRight().Text("المشاركون في المهمة:")
                    .FontSize(14).Bold().DirectionFromRightToLeft();

                column.Item().PaddingVertical(5);

                // الجدول
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(30);  // م
                        columns.RelativeColumn(3);   // الاسم
                        columns.RelativeColumn(2);   // رقم التلفون
                        columns.RelativeColumn(2);   // رقم الهوية
                        columns.RelativeColumn(1);   // نوعها
                        columns.RelativeColumn(2);   // الصفة
                    });

                    // رأس الجدول
                    table.Header(header =>
                    {
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("م").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("الاسم").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("رقم التلفون").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("رقم الهوية").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("نوعها").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("الصفة").FontSize(11).Bold().DirectionFromRightToLeft();
                    });

                    // بيانات الجدول
                    var participants = GetParticipants(visit);
                    for (int i = 0; i < participants.Count; i++)
                    {
                        var participant = participants[i];
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text((i + 1).ToString()).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignRight()
                            .Text(participant.Name).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(participant.PhoneNumber).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(participant.CardNumber).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(participant.CardType).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(participant.Position).FontSize(10).DirectionFromRightToLeft();
                    }
                });
            });
        }

        /// <summary>
        /// إنشاء جدول النقل
        /// </summary>
        private void CreateTransportTable(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                // عنوان الجدول
                column.Item().AlignRight().Text("وسائل النقل:")
                    .FontSize(14).Bold().DirectionFromRightToLeft();

                column.Item().PaddingVertical(5);

                // الجدول
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(30);  // م
                        columns.RelativeColumn(3);   // اسم السائق
                        columns.RelativeColumn(2);   // رقم التلفون
                        columns.RelativeColumn(2);   // رقم الهوية
                        columns.RelativeColumn(2);   // رقم اللوحة
                        columns.RelativeColumn(2);   // نوع المركبة
                    });

                    // رأس الجدول
                    table.Header(header =>
                    {
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("م").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("اسم السائق").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("رقم التلفون").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("رقم الهوية").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("رقم اللوحة").FontSize(11).Bold().DirectionFromRightToLeft();
                        header.Cell().Border(1).Padding(5).AlignCenter()
                            .Text("نوع المركبة").FontSize(11).Bold().DirectionFromRightToLeft();
                    });

                    // بيانات الجدول
                    var drivers = GetDrivers(visit);
                    for (int i = 0; i < drivers.Count; i++)
                    {
                        var driver = drivers[i];
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text((i + 1).ToString()).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignRight()
                            .Text(driver.Name).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(driver.PhoneNumber).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(driver.IdNumber).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(driver.PlateNumber).FontSize(10).DirectionFromRightToLeft();
                        table.Cell().Border(1).Padding(5).AlignCenter()
                            .Text(driver.VehicleType).FontSize(10).DirectionFromRightToLeft();
                    }
                });

                // نص التعاون
                column.Item().PaddingTop(10).AlignRight()
                    .Text("نأمل من الإخوة السائقين التعاون مع فريق العمل وتسهيل مهمتهم")
                    .FontSize(11).DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// إنشاء التوقيعات
        /// </summary>
        private void CreateSignatures(IContainer container)
        {
            container.PaddingTop(20).Row(row =>
            {
                // مدير المشروع
                row.RelativeItem().Column(col =>
                {
                    col.Item().AlignCenter().Text("مدير المشروع")
                        .FontSize(12).Bold().DirectionFromRightToLeft();
                    col.Item().PaddingTop(40);
                    col.Item().AlignCenter().Text("التوقيع: ........................")
                        .FontSize(10).DirectionFromRightToLeft();
                });

                // مساحة فاصلة
                row.ConstantItem(50);

                // مدير الفرع
                row.RelativeItem().Column(col =>
                {
                    col.Item().AlignCenter().Text("يعتمد مدير الفرع")
                        .FontSize(12).Bold().DirectionFromRightToLeft();
                    col.Item().PaddingTop(30);
                    col.Item().AlignCenter().Text("م/محمد محمد الديلمي")
                        .FontSize(10).DirectionFromRightToLeft();
                    col.Item().PaddingTop(10);
                    col.Item().AlignCenter().Text("التوقيع: ........................")
                        .FontSize(10).DirectionFromRightToLeft();
                });
            });
        }

        /// <summary>
        /// الحصول على المشاركين
        /// </summary>
        private List<ParticipantInfo> GetParticipants(FieldVisit visit)
        {
            using var context = new ApplicationDbContext();

            var participants = new List<ParticipantInfo>();

            // إضافة الموظفين المشاركين
            var officers = context.FieldVisitors
                .Where(fv => fv.FieldVisitId == visit.Id)
                .Join(context.Officers, fv => fv.OfficerId, o => o.Id, (fv, o) => o)
                .ToList();

            foreach (var officer in officers)
            {
                participants.Add(new ParticipantInfo
                {
                    Name = officer.Name,
                    PhoneNumber = officer.PhoneNumber ?? "غير محدد",
                    CardNumber = officer.CardNumber ?? "غير محدد",
                    CardType = officer.CardType ?? "هوية",
                    Position = officer.Rank ?? "موظف"
                });
            }

            // إضافة صف فارغ إذا لم توجد بيانات
            if (!participants.Any())
            {
                participants.Add(new ParticipantInfo
                {
                    Name = "غير محدد",
                    PhoneNumber = "غير محدد",
                    CardNumber = "غير محدد",
                    CardType = "هوية",
                    Position = "موظف"
                });
            }

            return participants;
        }

        /// <summary>
        /// الحصول على السائقين
        /// </summary>
        private List<DriverInfo> GetDrivers(FieldVisit visit)
        {
            using var context = new ApplicationDbContext();

            var drivers = new List<DriverInfo>();

            // البحث عن السائق الفائز
            var winnerQuote = context.DriverQuotes
                .Where(q => q.VisitNumber == visit.VisitNumber && q.Status == QuoteStatus.Accepted)
                .Join(context.Drivers, q => q.DriverId, d => d.Id, (q, d) => d)
                .FirstOrDefault();

            if (winnerQuote != null)
            {
                drivers.Add(new DriverInfo
                {
                    Name = winnerQuote.Name,
                    PhoneNumber = winnerQuote.PhoneNumber ?? "غير محدد",
                    IdNumber = winnerQuote.CardNumber ?? "غير محدد",
                    PlateNumber = winnerQuote.VehicleNumber ?? "غير محدد",
                    VehicleType = winnerQuote.VehicleType ?? "غير محدد"
                });
            }
            else
            {
                // إضافة صف فارغ إذا لم يوجد سائق
                drivers.Add(new DriverInfo
                {
                    Name = "لم يتم اختيار سائق بعد",
                    PhoneNumber = "غير محدد",
                    IdNumber = "غير محدد",
                    PlateNumber = "غير محدد",
                    VehicleType = "غير محدد"
                });
            }

            return drivers;
        }
    }

    /// <summary>
    /// بيانات التكليف
    /// </summary>
    public class AssignmentData
    {
        public FieldVisit Visit { get; set; }
        public List<ProjectInfo> Projects { get; set; } = new List<ProjectInfo>();
    }

    /// <summary>
    /// معلومات المشروع
    /// </summary>
    public class ProjectInfo
    {
        public string ProjectNumber { get; set; }
        public string ProjectName { get; set; }
    }

    /// <summary>
    /// معلومات المشارك
    /// </summary>
    public class ParticipantInfo
    {
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string CardNumber { get; set; }
        public string CardType { get; set; }
        public string Position { get; set; }
    }

    /// <summary>
    /// معلومات السائق
    /// </summary>
    public class DriverInfo
    {
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string IdNumber { get; set; }
        public string PlateNumber { get; set; }
        public string VehicleType { get; set; }
    }
}
