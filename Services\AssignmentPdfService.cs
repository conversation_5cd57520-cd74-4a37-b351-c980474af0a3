using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using System.Diagnostics;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إنشاء ملفات PDF للتكليف
    /// </summary>
    public class AssignmentPdfService
    {
        /// <summary>
        /// إنشاء ملف PDF للتكليف
        /// </summary>
        public async Task GenerateAssignmentPdfAsync(FieldVisit visit, string filePath = null)
        {
            try
            {
                // تكوين QuestPDF
                QuestPDF.Settings.License = LicenseType.Community;

                // جلب البيانات المطلوبة
                var assignmentData = await GetAssignmentDataAsync(visit);

                // إنشاء المستند
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(20);
                        page.DefaultTextStyle(x => x.FontFamily("Arial").FontSize(12));

                        // الهيدر الثابت
                        page.Header().Element(CreateHeader);

                        // المحتوى الرئيسي
                        page.Content().Column(column =>
                        {
                            // التاريخ
                            column.Item().PaddingTop(20).Element(c => CreateDateSection(c, visit));

                            // عنوان التكليف
                            column.Item().PaddingTop(20).Element(CreateTitle);

                            // نص التكليف
                            column.Item().PaddingTop(20).Element(c => CreateAssignmentText(c, visit, assignmentData));

                            // تفاصيل المشروع
                            column.Item().PaddingTop(15).Element(c => CreateProjectDetails(c, visit, assignmentData));

                            // النشاط
                            column.Item().PaddingTop(10).Element(c => CreateActivitySection(c, visit));

                            // خط السير
                            column.Item().PaddingTop(10).Element(c => CreateItinerary(c, visit));

                            // تاريخ التحرك
                            column.Item().PaddingTop(10).Element(c => CreateMovementDate(c, visit));

                            // جدول المشاركين
                            column.Item().PaddingTop(20).Element(c => CreateParticipantsTable(c, assignmentData.Participants));

                            // جدول السائق
                            column.Item().PaddingTop(15).Element(c => CreateDriverTable(c, assignmentData.Driver));

                            // التوقيعات
                            column.Item().PaddingTop(30).Element(c => CreateSignatures(c, assignmentData));
                        });

                        // الفوتر الثابت
                        page.Footer().Element(CreateFooter);
                    });
                });

                // حفظ الملف
                string finalFilePath;
                if (string.IsNullOrEmpty(filePath))
                {
                    var fileName = $"تكليف_الزيارة_{visit.VisitNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                    var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                    finalFilePath = Path.Combine(desktopPath, fileName);
                }
                else
                {
                    finalFilePath = filePath;
                }

                await Task.Run(() => document.GeneratePdf(finalFilePath));

                // فتح الملف
                Process.Start(new ProcessStartInfo(finalFilePath) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف PDF للتكليف: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// جلب بيانات التكليف من قاعدة البيانات
        /// </summary>
        private async Task<AssignmentData> GetAssignmentDataAsync(FieldVisit visit)
        {
            using var context = new ApplicationDbContext();

            // جلب المشاركين
            var participants = await context.FieldVisitors
                .Where(fv => fv.FieldVisitId == visit.Id)
                .ToListAsync();

            // جلب المشاريع
            var projects = await context.FieldVisitProjects
                .Where(fvp => fvp.FieldVisitId == visit.Id)
                .ToListAsync();

            // جلب السائق الفائز
            var winnerQuote = await context.DriverQuotes
                .Where(dq => dq.VisitNumber == visit.VisitNumber && dq.Status == QuoteStatus.Accepted)
                .FirstOrDefaultAsync();

            Driver driver = null;
            if (winnerQuote != null)
            {
                driver = await context.Drivers
                    .FirstOrDefaultAsync(d => d.Name == winnerQuote.DriverName || d.DriverCode == winnerQuote.DriverCode);
            }

            return new AssignmentData
            {
                Participants = participants,
                Projects = projects,
                Driver = driver,
                WinnerQuote = winnerQuote
            };
        }

        /// <summary>
        /// إنشاء الهيدر
        /// </summary>
        private void CreateHeader(IContainer container)
        {
            container.Row(row =>
            {
                // المعلومات الإنجليزية (يسار)
                row.RelativeItem(2).Column(column =>
                {
                    column.Item().AlignLeft().Text("Republic Of YEMEN").FontSize(12).Bold();
                    column.Item().AlignLeft().Text("Social Fund For Development").FontSize(10);
                    column.Item().AlignLeft().Text("Presidency of Council of Ministers").FontSize(10);
                    column.Item().AlignLeft().Text("Dhamar Albidaa Branch").FontSize(10);
                });

                // الشعار (وسط)
                row.RelativeItem(1).AlignCenter().Column(column =>
                {
                    // جرب عدة مسارات محتملة للشعار
                    var possiblePaths = new[]
                    {
                        @"C:\Users\<USER>\Desktop\sys\Icons\sfd.png",
                        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icons", "sfd.png"),
                        Path.Combine(Directory.GetCurrentDirectory(), "Icons", "sfd.png"),
                        Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "Icons", "sfd.png")
                    };

                    var logoPath = possiblePaths.FirstOrDefault(File.Exists);

                    if (!string.IsNullOrEmpty(logoPath))
                    {
                        // عرض الشعار الحقيقي
                        column.Item().AlignCenter().Width(80).Height(80).Image(logoPath);
                    }
                    else
                    {
                        // عرض نص بديل إذا لم يوجد الشعار
                        column.Item().AlignCenter().Border(1).BorderColor("#000000")
                            .Width(80).Height(80).AlignCenter().AlignMiddle()
                            .Text("SFD").FontSize(20).Bold().FontColor("#0066CC");
                    }

                });

                // المعلومات العربية (يمين)
                row.RelativeItem(2).Column(column =>
                {
                    column.Item().AlignRight().Text("الجمهورية اليمنية").FontSize(14).Bold();
                    column.Item().AlignRight().Text("رئاسة مجلس الوزراء").FontSize(12);
                    column.Item().AlignRight().Text("الصندوق الاجتماعي للتنمية").FontSize(12);
                    column.Item().AlignRight().Text("فرع ذمار والبيضاء").FontSize(12);
                });
            });
        }

        /// <summary>
        /// إنشاء قسم التاريخ
        /// </summary>
        private void CreateDateSection(IContainer container, FieldVisit visit)
        {
            container.Column(column =>
            {
                column.Item().AlignRight().Text($"التاريخ: {visit.AddDate:dd/MM/yyyy}م").FontSize(12);
                column.Item().AlignRight().Text($"الموافق: {visit.HijriDate}هـ").FontSize(12);
            });
        }

        /// <summary>
        /// إنشاء عنوان التكليف
        /// </summary>
        private void CreateTitle(IContainer container)
        {
            container.Column(column =>
            {
                // العنوان الرئيسي
                column.Item().AlignCenter().Text("تكليف")
                    .FontSize(24).Bold().FontColor("#000000");

                // الخط تحت العنوان
                column.Item().PaddingTop(5).AlignCenter().Width(80).Height(3).Background("#000000");
            });
        }

        /// <summary>
        /// إنشاء نص التكليف
        /// </summary>
        private void CreateAssignmentText(IContainer container, FieldVisit visit, AssignmentData data)
        {
            container.Column(column =>
            {
                // النص الرئيسي للتكليف
                column.Item().AlignRight().Text("يكلف الصندوق الاجتماعي للتنمية ـ فرع ذمار أبيدان الإخوة المبين أسماؤهم في الجدول")
                    .FontSize(14).Bold().LineHeight(1.6f).DirectionFromRightToLeft();

                // السطر الثاني
                column.Item().PaddingTop(5).AlignRight().Text("أدناه لتنفيذ المهمة التالية :-")
                    .FontSize(14).Bold().LineHeight(1.6f).DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// إنشاء تفاصيل المشروع
        /// </summary>
        private void CreateProjectDetails(IContainer container, FieldVisit visit, AssignmentData data)
        {
            container.Column(column =>
            {
                var projectText = data.Projects.Any()
                    ? string.Join(" - ", data.Projects.Select(p => $"{p.ProjectNumber} - {p.ProjectName}"))
                    : "تأهيل وتحسين طريق وحصاد مياه بقرية ظلمان ـ عزلة الرعية ـ المنار ـ ذمار";

                column.Item().AlignRight().Text($"• المشروع : {projectText}")
                    .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// إنشاء قسم النشاط
        /// </summary>
        private void CreateActivitySection(IContainer container, FieldVisit visit)
        {
            var activityText = !string.IsNullOrEmpty(visit.MissionPurpose)
                ? visit.MissionPurpose
                : "المتابعه المجتمعية وحصر الاسر الغير عاملة والغير متواجدة";

            container.AlignRight().Text($"• النشــاط : {activityText}")
                .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
        }

        /// <summary>
        /// إنشاء خط السير
        /// </summary>
        private void CreateItinerary(IContainer container, FieldVisit visit)
        {
            var itineraryText = visit.Itinerary != null && visit.Itinerary.Any()
                ? string.Join(" - ", visit.Itinerary)
                : "ذمار معبر ضوران المنار قرية ظلمان عزلة الرعية";

            container.AlignRight().Text($"• خط السير : {itineraryText}")
                .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
        }

        /// <summary>
        /// إنشاء تاريخ التحرك
        /// </summary>
        private void CreateMovementDate(IContainer container, FieldVisit visit)
        {
            container.AlignRight().Text($"• تاريخ التحرك : مــن  {visit.DepartureDate:dd/MM/yyyy}  الى  {visit.ReturnDate:dd/MM/yyyy}")
                .FontSize(13).Bold().LineHeight(1.5f).DirectionFromRightToLeft();
        }

        /// <summary>
        /// إنشاء جدول المشاركين
        /// </summary>
        private void CreateParticipantsTable(IContainer container, List<FieldVisitor> participants)
        {
            container.Column(column =>
            {
                // عنوان البيان مع خط تحته
                column.Item().PaddingBottom(10).Column(subColumn =>
                {
                    subColumn.Item().AlignRight().Text("البيــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــان")
                        .FontSize(16).Bold().DirectionFromRightToLeft();

                    // خط تحت البيان
                    subColumn.Item().PaddingTop(3).AlignRight().Width(500).Height(2).Background("#000000");
                });

                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn(2); // الصفة
                        columns.RelativeColumn(2); // نوعها
                        columns.RelativeColumn(2); // رقم الهوية
                        columns.RelativeColumn(2); // رقم التلفون
                        columns.RelativeColumn(3); // الاسم
                        columns.RelativeColumn(1); // م
                    });

                    // رأس الجدول
                    table.Header(header =>
                    {
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("الصفة").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("نوعها").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("رقم الهوية").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("رقم التلفون").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("الاسم").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("م").FontSize(11).Bold();
                    });

                    // صفوف البيانات - عرض المشاركين الفعليين فقط
                    for (int i = 0; i < participants.Count; i++)
                    {
                        var participant = participants[i];

                        table.Cell().Border(1).BorderColor("#CCCCCC")
                            .Padding(5).AlignCenter().Text(participant.OfficerRank ?? "موظف").FontSize(10);
                        table.Cell().Border(1).BorderColor("#CCCCCC")
                            .Padding(5).AlignCenter().Text("بطاقة شخصية").FontSize(10);
                        table.Cell().Border(1).BorderColor("#CCCCCC")
                            .Padding(5).AlignCenter().Text("").FontSize(10); // رقم الهوية - غير متوفر
                        table.Cell().Border(1).BorderColor("#CCCCCC")
                            .Padding(5).AlignCenter().Text(participant.PhoneNumber ?? "").FontSize(10);
                        table.Cell().Border(1).BorderColor("#CCCCCC")
                            .Padding(5).AlignRight().Text(participant.OfficerName ?? "").FontSize(10);
                        table.Cell().Border(1).BorderColor("#CCCCCC")
                            .Padding(5).AlignCenter().Text((i + 1).ToString()).FontSize(10);
                    }
                });
            });
        }

        /// <summary>
        /// إنشاء جدول السائق
        /// </summary>
        private void CreateDriverTable(IContainer container, Driver driver)
        {
            container.Column(column =>
            {
                column.Item().PaddingBottom(10).Text("وسائل النقل:")
                    .FontSize(12).Bold();

                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn(2); // نوع السيارة
                        columns.RelativeColumn(2); // رقم السيارة
                        columns.RelativeColumn(2); // رقم الهوية
                        columns.RelativeColumn(2); // رقم الهاتف
                        columns.RelativeColumn(2); // اسم السائق
                    });

                    // رأس الجدول
                    table.Header(header =>
                    {
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("نوع السيارة").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("رقم السيارة").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("رقم الهوية").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("رقم الهاتف").FontSize(11).Bold();
                        header.Cell().Border(1).BorderColor("#000000").Background("#F8F9FA")
                            .Padding(5).AlignCenter().Text("اسم السائق").FontSize(11).Bold();
                    });

                    // صف البيانات
                    table.Cell().Border(1).BorderColor("#CCCCCC")
                        .Padding(5).AlignCenter().Text(driver?.VehicleType ?? "لم يتم اختيار سائق بعد").FontSize(10);
                    table.Cell().Border(1).BorderColor("#CCCCCC")
                        .Padding(5).AlignCenter().Text(driver?.VehicleNumber ?? "").FontSize(10);
                    table.Cell().Border(1).BorderColor("#CCCCCC")
                        .Padding(5).AlignCenter().Text(driver?.CardNumber ?? "").FontSize(10);
                    table.Cell().Border(1).BorderColor("#CCCCCC")
                        .Padding(5).AlignCenter().Text(driver?.PhoneNumber ?? "").FontSize(10);
                    table.Cell().Border(1).BorderColor("#CCCCCC")
                        .Padding(5).AlignCenter().Text(driver?.Name ?? "لم يتم اختيار سائق بعد").FontSize(10);
                });

                // نص البيان
                column.Item().PaddingTop(10).AlignRight().Text("البيان")
                    .FontSize(12).LineHeight(1.3f).DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// إنشاء التوقيعات
        /// </summary>
        private void CreateSignatures(IContainer container, AssignmentData data)
        {
            container.Row(row =>
            {
                // مدير الفرع
                row.RelativeItem().Column(column =>
                {
                    column.Item().AlignCenter().Text("مدير الفرع").FontSize(12).Bold();
                    column.Item().PaddingTop(30).AlignCenter().Text("م/محمد محمد الديلمي").FontSize(11);
                });
            });
        }

        /// <summary>
        /// إنشاء الفوتر
        /// </summary>
        private void CreateFooter(IContainer container)
        {
            container.BorderTop(1).BorderColor("#000000").PaddingTop(10).Column(column =>
            {
                // السطر الأول - المعلومات العربية
                column.Item().Row(row =>
                {
                    row.RelativeItem().AlignRight().Text("الصندوق الاجتماعي للتنمية - فرع ذمار - خط صنعاء تعز - جولة تكمان - عمارة الوقف - هاتف: ٥٠٣٠٤٥ / فاكس: ٥٠٣٠٤٧ / ص.ب: ٨٧٤٠٠")
                        .FontSize(9).Bold().DirectionFromRightToLeft();
                });

                // السطر الثاني - أرقام الهواتف
                column.Item().PaddingTop(2).Row(row =>
                {
                    row.RelativeItem().AlignRight().Text("الرقم المجاني بتشغيلي ١٧٧٠٤٥٩٦٢ أو الرقم المجاني للتشغيل ٨٠٠٩٨٠٠")
                        .FontSize(9).DirectionFromRightToLeft();
                });

                // السطر الثالث - المعلومات الإنجليزية
                column.Item().PaddingTop(2).Row(row =>
                {
                    row.RelativeItem().AlignLeft().Text("Social Fund for Development / Dhamar Branch – Taiz Sana'a Street – Camran Round - P.O. Box: 87400 Tel: 503045 – Fax: 503047")
                        .FontSize(9).Bold();
                });

                // السطر الرابع - الإيميل والرقم المجاني
                column.Item().PaddingTop(2).Row(row =>
                {
                    row.RelativeItem().AlignLeft().Text("Free number: 8009800 – Email: <EMAIL>")
                        .FontSize(9);
                });
            });
        }
    }

    /// <summary>
    /// بيانات التكليف
    /// </summary>
    public class AssignmentData
    {
        public List<FieldVisitor> Participants { get; set; } = new();
        public List<FieldVisitProject> Projects { get; set; } = new();
        public Driver Driver { get; set; }
        public DriverQuote WinnerQuote { get; set; }
    }
}
