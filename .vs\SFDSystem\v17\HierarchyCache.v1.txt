﻿++Solution 'SFDSystem' ‎ (1 of 1 project)
i:{00000000-0000-0000-0000-000000000000}:SFDSystem.sln
++SFDSystem
i:{00000000-0000-0000-0000-000000000000}:SFDSystem
++Dependencies
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4653
++Properties
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\properties\
++Assets
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\assets\
++Converters
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\
++Data
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\data\
++Extensions
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\extensions\
++Helpers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\helpers\
++Icons
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\icons\
++Migrations
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\
++Models
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\
++Resources
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\resources\
++Services
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\
++AdvancedPrintService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\advancedprintservice.cs
++AuthenticationService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\authenticationservice.cs
++AutoBackupService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\autobackupservice.cs
++BatchExcelImportService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\batchexcelimportservice.cs
++DatabaseSeeder.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\databaseseeder.cs
++DatabaseService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\databaseservice.cs
++DataService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\dataservice.cs
++DriverAdjustmentPdfService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\driveradjustmentpdfservice.cs
++DriverAdjustmentService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\driveradjustmentservice.cs
++DriverDataService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\driverdataservice.cs
++ErrorHandlingService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\errorhandlingservice.cs
++ExcelImportService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\excelimportservice.cs
++ExcelValidationService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\excelvalidationservice.cs
++FieldVisitService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\fieldvisitservice.cs
++HijriDateService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\hijridateservice.cs
++IDataService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\idataservice.cs
++IFieldVisitService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\ifieldvisitservice.cs
++ImportLogService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\importlogservice.cs
++InitialDataSeeder.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\initialdataseeder.cs
++IOffersService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\ioffersservice.cs
++OffersService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\offersservice.cs
++ProjectsDataSeeder.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\projectsdataseeder.cs
++ProjectsSeeder.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\projectsseeder.cs
++QuickAccessService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\quickaccessservice.cs
++ReportViewPrintService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\reportviewprintservice.cs
++UserService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\userservice.cs
++WhatsAppAutomationService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\whatsappautomationservice.cs
++WhatsAppService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\whatsappservice.cs
++SFD
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\sfd\
++Styles
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\styles\
++ViewModels
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\sfd\viewmodels\
++Views
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\
++AddColumnsToDatabase.sql
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\addcolumnstodatabase.sql
++app.manifest
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\app.manifest
++app.py
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\app.py
++App.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\app.xaml
++AssemblyInfo.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\assemblyinfo.cs
++DriverAdjustment_README.md
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\driveradjustment_readme.md
++fix-xaml-errors.ps1
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\fix-xaml-errors.ps1
++MainWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\mainwindow.xaml
++Program.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\program.cs
++تشغيل_النظام.bat
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\تشغيل_النظام.bat
++Analyzers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4654
++Frameworks
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4668
++Packages
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4671
++PublishProfiles
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\properties\publishprofiles\
++Settings.Designer.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\properties\settings.designer.cs
++Settings.settings
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\properties\settings.settings
++SystemLogo.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\assets\systemlogo.xaml
++BoolToColorConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\booltocolorconverter.cs
++ColorToBrushConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\colortobrushconverter.cs
++FileNameConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\filenameconverter.cs
++ProfessionalConverters.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\professionalconverters.cs
++SidebarConverters.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\sidebarconverters.cs
++TrimStringConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\trimstringconverter.cs
++ValueConverters.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\converters\valueconverters.cs
++ApplicationDbContext.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\data\applicationdbcontext.cs
++DatabaseConfig.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\data\databaseconfig.cs
++SeedDriversData.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\data\seeddriversdata.cs
++ExcelExtensions.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\extensions\excelextensions.cs
++DateHelper.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\helpers\datehelper.cs
++FieldVisitDiagnosticHelper.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\helpers\fieldvisitdiagnostichelper.cs
++MultiPageDocumentPaginator.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\helpers\multipagedocumentpaginator.cs
++NumberToArabicTextHelper.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\helpers\numbertoarabictexthelper.cs
++VisitIdMigrationHelper.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\helpers\visitidmigrationhelper.cs
++sfd.png
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\icons\sfd.png
++AddApprovalAndSubmissionFields.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\addapprovalandsubmissionfields.cs
++AddDriverAdjustmentsTable.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\adddriveradjustmentstable.cs
++AddDriverLicenseFields.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\adddriverlicensefields.cs
++AddEntryNumberField.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\addentrynumberfield.cs
++AddItineraryTextColumn.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\additinerarytextcolumn.cs
++AddMessageDocumentationTables.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\addmessagedocumentationtables.cs
++AddWinnerDriverMessageColumn.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\addwinnerdrivermessagecolumn.cs
++ApplicationDbContextModelSnapshot.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\applicationdbcontextmodelsnapshot.cs
++FixDriverNamesInQuotes.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\fixdrivernamesinquotes.cs
++RemoveUnusedDriverQuoteColumns.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\migrations\removeunuseddriverquotecolumns.cs
++ContractTemplate.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\contracttemplate.cs
++Driver.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\driver.cs
++DriverAdjustment.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\driveradjustment.cs
++DriverOffer.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\driveroffer.cs
++DriverQuote.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\driverquote.cs
++DropData.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\dropdata.cs
++FieldVisit.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\fieldvisit.cs
++FieldVisitItinerary.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\fieldvisititinerary.cs
++FieldVisitProject.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\fieldvisitproject.cs
++ItineraryDay.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\itineraryday.cs
++MessageDocumentation.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\messagedocumentation.cs
++Officer.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\officer.cs
++ProfessionalTemplate.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\professionaltemplate.cs
++Project.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\project.cs
++QuickAccessModels.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\quickaccessmodels.cs
++ReportModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\reportmodel.cs
++Sector.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\sector.cs
++User.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\user.cs
++Vehicle.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\models\vehicle.cs
++Colors.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\resources\colors.xaml
++Styles.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\resources\styles.xaml
++LoginStyles.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\styles\loginstyles.xaml
++PrintStyles.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\styles\printstyles.xaml
++AddProjectViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\addprojectviewmodel.cs
++DashboardViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\dashboardviewmodel.cs
++DriverAdjustmentViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\driveradjustmentviewmodel.cs
++DriverPricingViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\driverpricingviewmodel.cs
++DropDataViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\dropdataviewmodel.cs
++FieldVisitsLogViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\fieldvisitslogviewmodel.cs
++FieldVisitViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\fieldvisitviewmodel.cs
++LoginViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\loginviewmodel.cs
++MainViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\mainviewmodel.cs
++MessageDocumentationViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\messagedocumentationviewmodel.cs
++OffersViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\offersviewmodel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\sfd\viewmodels\offersviewmodel.cs
++PowerfulMessageDocumentationViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\powerfulmessagedocumentationviewmodel.cs
++ProfessionalMessageDocumentationViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\professionalmessagedocumentationviewmodel.cs
++ProfessionalMessagesViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\professionalmessagesviewmodel.cs
++QuickAccessViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\quickaccessviewmodel.cs
++ReportViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\reportviewmodel.cs
++SectorManagementViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\sectormanagementviewmodel.cs
++StatisticsViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\statisticsviewmodel.cs
++VehicleManagementViewModel.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\viewmodels\vehiclemanagementviewmodel.cs
++AddOfficersWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\addofficerswindow.xaml
++AddProjectWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\addprojectwindow.xaml
++AddUserWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\adduserwindow.xaml
++BatchImportResultsWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\batchimportresultswindow.xaml
++BatchImportWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\batchimportwindow.xaml
++BoolToMessageTooltipConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\booltomessagetooltipconverter.cs
++DashboardView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\dashboardview.xaml
++DatabaseConfigWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\databaseconfigwindow.xaml
++DriverAdjustmentWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\driveradjustmentwindow.xaml
++DropDataView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\dropdataview.xaml
++EditUserWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\edituserwindow.xaml
++EmptyStringToVisibilityConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\emptystringtovisibilityconverter.cs
++ExcelPreviewWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\excelpreviewwindow.xaml
++FieldVisitsLogView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\fieldvisitslogview.xaml
++ImportStatisticsWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\importstatisticswindow.xaml
++InitialSetupWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\initialsetupwindow.xaml
++LoginWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\loginwindow.xaml
++NullToVisibilityConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\nulltovisibilityconverter.cs
++OffersWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\offerswindow.xaml
++PowerfulMessageDocumentationWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\powerfulmessagedocumentationwindow.xaml
++PrintSettingsWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\printsettingswindow.xaml
++ProfessionalDriverManagementWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\professionaldrivermanagementwindow.xaml
++ProfessionalMessagesWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\professionalmessageswindow.xaml
++QuickAccessView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\quickaccessview.xaml
++ReportView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\reportview.xaml
++ReportWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\reportwindow.xaml
++RowIndexConverter.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\rowindexconverter.cs
++SectorManagementWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\sectormanagementwindow.xaml
++SimpleUserManagementView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\simpleusermanagementview.xaml
++SystemDashboardWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\systemdashboardwindow.xaml
++UserManagementView.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\usermanagementview.xaml
++UserPermissionsWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\userpermissionswindow.xaml
++ValidationResultWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\validationresultwindow.xaml
++VehicleManagementWindow.xaml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\vehiclemanagementwindow.xaml
++App.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\app.xaml.cs
++MainWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\mainwindow.xaml.cs
++Microsoft.CodeAnalysis.Analyzers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\sdk\9.0.108\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\sdk\9.0.108\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.6\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4670
++Microsoft.WindowsDesktop.App.WPF
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4669
++ClosedXML (0.104.1)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4680
++EPPlus (8.0.7)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4678
++iTextSharp.LGPLv2.Core (3.7.4)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4679
++Microsoft.EntityFrameworkCore.Design (9.0.6)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4676
++Microsoft.EntityFrameworkCore.SqlServer (9.0.6)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4675
++PdfSharp (6.0.0)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4674
++Prism.Wpf (9.0.537)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4672
++QuestPDF (2025.7.0)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4681
++System.Drawing.Common (9.0.0)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4673
++System.Net.Http (4.3.4)
i:{27326ff1-7897-4073-a59c-c84c0750677f}:>4677
++FolderProfile.pubxml
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\properties\publishprofiles\folderprofile.pubxml
++AddOfficersWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\addofficerswindow.xaml.cs
++AddProjectWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\addprojectwindow.xaml.cs
++AddUserWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\adduserwindow.xaml.cs
++BatchImportResultsWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\batchimportresultswindow.xaml.cs
++BatchImportWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\batchimportwindow.xaml.cs
++DashboardView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\dashboardview.xaml.cs
++DatabaseConfigWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\databaseconfigwindow.xaml.cs
++DriverAdjustmentWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\driveradjustmentwindow.xaml.cs
++DropDataView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\dropdataview.xaml.cs
++EditUserWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\edituserwindow.xaml.cs
++ExcelPreviewWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\excelpreviewwindow.xaml.cs
++FieldVisitsLogView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\fieldvisitslogview.xaml.cs
++ImportStatisticsWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\importstatisticswindow.xaml.cs
++InitialSetupWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\initialsetupwindow.xaml.cs
++LoginWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\loginwindow.xaml.cs
++OffersWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\offerswindow.xaml.cs
++PowerfulMessageDocumentationWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\powerfulmessagedocumentationwindow.xaml.cs
++PrintSettingsWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\printsettingswindow.xaml.cs
++ProfessionalDriverManagementWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\professionaldrivermanagementwindow.xaml.cs
++ProfessionalMessagesWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\professionalmessageswindow.xaml.cs
++QuickAccessView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\quickaccessview.xaml.cs
++ReportView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\reportview.xaml.cs
++ReportWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\reportwindow.xaml.cs
++SectorManagementWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\sectormanagementwindow.xaml.cs
++SimpleUserManagementView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\simpleusermanagementview.xaml.cs
++SystemDashboardWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\systemdashboardwindow.xaml.cs
++UserManagementView.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\usermanagementview.xaml.cs
++UserPermissionsWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\userpermissionswindow.xaml.cs
++ValidationResultWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\validationresultwindow.xaml.cs
++VehicleManagementWindow.xaml.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\views\vehiclemanagementwindow.xaml.cs
++AssignmentPdfService.cs
i:{27326ff1-7897-4073-a59c-c84c0750677f}:c:\users\<USER>\desktop\sys\services\assignmentpdfservice.cs
