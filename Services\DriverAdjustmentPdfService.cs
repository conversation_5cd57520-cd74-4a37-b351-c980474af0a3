using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Linq;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إنشاء ملفات PDF للإضافات والخصومات للسائقين
    /// </summary>
    public class DriverAdjustmentPdfService
    {
        /// <summary>
        /// إنشاء ملف PDF للإضافة أو الخصم
        /// </summary>
        public async Task GenerateDriverAdjustmentPdfAsync(FieldVisit visit, string adjustmentType, int adjustmentDays, string reason, DateTime? newReturnDate = null, decimal? customAmount = null, string filePath = null)
        {
            try
            {
                // تكوين QuestPDF
                QuestPDF.Settings.License = LicenseType.Community;

                // إنشاء المستند
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(20);
                        page.DefaultTextStyle(x => x.FontFamily("Arial"));

                        page.Content().Column(column =>
                        {
                            // الهيدر
                            CreateProfessionalHeader(column.Item(), $"أمر تغييري مهمة ميدانية ({adjustmentType}) ", visit);

                            column.Item().PaddingVertical(5);

                            // معلومات المشروع
                            CreateProjectInfoSection(column.Item(), visit);

                            column.Item().PaddingVertical(4);

                            // معلومات السائق مع تاريخ العودة المعدل
                            CreateDriverInfoSection(column.Item(), visit, adjustmentType, adjustmentDays, newReturnDate);

                            column.Item().PaddingVertical(4);

                            // تفاصيل الإضافة/الخصم
                            CreateAdjustmentDetailsSection(column.Item(), adjustmentType, adjustmentDays, visit, customAmount);

                            column.Item().PaddingVertical(4);

                            // السبب
                            CreateReasonSection(column.Item(), reason);

                            column.Item().PaddingVertical(1);

                            // التوقيعات
                            CreateSignaturesSection(column.Item(), visit);
                        });
                    });
                });

                // حفظ الملف
                string finalFilePath;
                if (string.IsNullOrEmpty(filePath))
                {
                    // إذا لم يتم تمرير مسار، استخدم المسار الافتراضي
                    var fileName = $"تعديل_السائق_{adjustmentType}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                    var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                    finalFilePath = Path.Combine(desktopPath, fileName);
                }
                else
                {
                    finalFilePath = filePath;
                }

                await Task.Run(() => document.GeneratePdf(finalFilePath));

                // فتح الملف
                Process.Start(new ProcessStartInfo(finalFilePath) { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف PDF: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء الهيدر الاحترافي للطباعة
        /// </summary>
        private static void CreateProfessionalHeader(IContainer container, string title, FieldVisit visit)
        {
            container.Column(column =>
            {
                // الهيدر العلوي منظم
                column.Item().Border(1).BorderColor("#000000").Padding(10).Row(row =>
                {
                    // التاريخ ورقم الزيارة (يسار)
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().PaddingTop(5).AlignLeft().Text($"التاريخ: {DateTime.Now:dd/MM/yyyy}")
                            .FontSize(11).DirectionFromRightToLeft();
                        col.Item().PaddingTop(8).AlignLeft().Text($"{visit.VisitNumber} :رقم الزيارة")
                            .FontSize(11).DirectionFromLeftToRight();
                    });

                    // الشعار (وسط)
                    row.RelativeItem().Column(col =>
                    {
                        try
                        {
                            // جرب عدة مسارات محتملة للشعار
                            var possiblePaths = new[]
                            {
                                @"C:\Users\<USER>\Desktop\sys\Icons\sfd.png",
                                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icons", "sfd.png"),
                                Path.Combine(Directory.GetCurrentDirectory(), "Icons", "sfd.png"),
                                Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "Icons", "sfd.png")
                            };

                            string logoPath = null;
                            foreach (var path in possiblePaths)
                            {
                                if (File.Exists(path))
                                {
                                    logoPath = path;
                                    break;
                                }
                            }

                            if (!string.IsNullOrEmpty(logoPath))
                            {
                                col.Item().AlignCenter().Height(60).Image(logoPath);
                            }
                        }
                        catch
                        {
                            // لا شيء - لا نعرض أي شعار في حالة الخطأ
                        }
                    });

                    // المؤسسة (يمين)
                    row.RelativeItem().Column(col =>
                    {
                        col.Item().PaddingTop(5).AlignRight().Text("الصندوق الاجتماعي للتنمية")
                            .FontSize(13).Bold().DirectionFromRightToLeft();
                        col.Item().PaddingTop(8).AlignRight().Text("فرع ذمار والبيضاء")
                            .FontSize(11).DirectionFromRightToLeft();
                    });
                });

                column.Item().PaddingVertical(4);

                // العنوان الرئيسي
                column.Item()
                    .Border(1)
                    .BorderColor("#000000")
                    .Padding(8)
                    .AlignCenter()
                    .Text($"{title}")
                    .FontSize(15)
                    .Bold()
                    .DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// إنشاء قسم معلومات المشروع
        /// </summary>
        private static void CreateProjectInfoSection(IContainer container, FieldVisit visit)
        {
            container.Border(1).BorderColor("#000000").Column(col =>
            {
                // عنوان القسم
                col.Item().Background("#E8E8E8").Padding(8).AlignCenter()
                    .Text("معلومات المشروع").FontSize(12).Bold().DirectionFromRightToLeft();

                // محتوى المشروع
                col.Item().Padding(8).Column(content =>
                {
                    // عرض معلومات المشروع الأول إذا كان متوفراً
                    var firstProject = visit.Projects?.FirstOrDefault();
                    if (firstProject != null)
                    {
                        content.Item().AlignRight().Text($"{firstProject.ProjectNumber} :رقم المشروع")
                            .FontSize(11).DirectionFromLeftToRight();
                        content.Item().PaddingVertical(3);
                        content.Item().AlignRight().Text($"اسم المشروع: {firstProject.ProjectName}")
                            .FontSize(11).DirectionFromRightToLeft();
                        content.Item().PaddingVertical(3);
                    }
                    content.Item().AlignRight().Text($"طبيعة المهمة: {visit.MissionPurpose}")
                        .FontSize(11).DirectionFromRightToLeft();
                });
            });
        }

        /// <summary>
        /// إنشاء قسم معلومات السائق الفائز مع تاريخ العودة المعدل
        /// </summary>
        private static void CreateDriverInfoSection(IContainer container, FieldVisit visit, string adjustmentType, int adjustmentDays, DateTime? newReturnDate = null)
        {
            container.Border(1).BorderColor("#000000").Column(col =>
            {
                // عنوان القسم
                col.Item().Background("#E8E8E8").Padding(8).AlignCenter()
                    .Text("تفاصيل الزيارة الميدانية").FontSize(12).Bold().DirectionFromLeftToRight();

                // محتوى السائق منظم ومنسق
                col.Item().Padding(10).Column(content =>
                {
                    // استخراج السائق الفائز فقط من SelectedDrivers
                    var winnerDriverName = ExtractWinnerDriverName(visit.SelectedDrivers);

                    // البحث عن رقم التلفون من قاعدة البيانات
                    var phoneNumber = GetDriverPhoneNumber(winnerDriverName);

                    // القائمين بالزيارة (جميع الأسماء)
                    var visitConductors = visit.Visitors?.Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                                                        ?.Select(v => v.OfficerName)
                                                        ?.ToList() ?? new List<string>();
                    var visitConductor = visitConductors.Any() ? string.Join(" - ", visitConductors) : "غير محدد";

                    // استخدام تاريخ العودة المعدل المرسل أو حسابه
                    DateTime modifiedReturnDate;
                    if (newReturnDate.HasValue)
                    {
                        modifiedReturnDate = newReturnDate.Value;
                    }
                    else
                    {
                        // حساب تاريخ العودة المعدل كبديل
                        if (adjustmentType == "الإضافة")
                        {
                            modifiedReturnDate = visit.ReturnDate.AddDays(adjustmentDays);
                        }
                        else // الخصم
                        {
                            modifiedReturnDate = visit.ReturnDate.AddDays(-adjustmentDays);
                        }
                    }
                    var returnDate = visit.ReturnDate.ToString("dd/MM/yyyy");
                    var modifiedReturnDateStr = modifiedReturnDate.ToString("dd/MM/yyyy");

                    // السطر الأول: رقم التلفون + السائق + القائم بالمهمة
                    content.Item().Row(row =>
                    {
                        // رقم التلفون
                        row.RelativeItem(1).Border(0.5f).BorderColor("#CCCCCC").Padding(6)
                            .AlignRight().Text($"رقم التلفون: {phoneNumber}")
                            .FontSize(11).DirectionFromRightToLeft();

                        // مساحة فاصلة
                        row.ConstantItem(10);

                        // السائق
                        row.RelativeItem(1).Border(0.5f).BorderColor("#CCCCCC").Padding(6)
                            .AlignRight().Text($"السائق: {winnerDriverName}")
                            .FontSize(11).DirectionFromRightToLeft();

                        // مساحة فاصلة
                        row.ConstantItem(10);

                        // القائم بالمهمة
                        row.RelativeItem(1).Border(0.5f).BorderColor("#CCCCCC").Padding(6)
                            .AlignRight().Text($"القائم بالمهمة: {visitConductor}")
                            .FontSize(11).DirectionFromRightToLeft();
                    });

                    // مساحة فارغة للفصل
                    content.Item().Height(10);

                    // السطر الثاني: التواريخ ومدة النزول
                    content.Item().Row(row =>
                    {
                        // تاريخ العودة المعدل (سيظهر على اليسار)
                        row.RelativeItem(1).Border(0.5f).BorderColor("#CCCCCC").Padding(6)
                            .AlignRight().Text($"تاريخ العودة المعدل: {modifiedReturnDateStr}")
                            .FontSize(11).DirectionFromRightToLeft();

                        // مساحة فاصلة
                        row.ConstantItem(10);

                        // تاريخ العودة الأصلي (سيظهر في الوسط)
                        row.RelativeItem(1).Border(0.5f).BorderColor("#CCCCCC").Padding(6)
                            .AlignRight().Text($"تاريخ العودة الفعلي: {returnDate}")
                            .FontSize(11).DirectionFromRightToLeft();

                        // مساحة فاصلة
                        row.ConstantItem(10);

                        // مدة النزول (سيظهر على اليمين)
                        row.RelativeItem(1).Border(0.5f).BorderColor("#CCCCCC").Padding(6)
                            .AlignRight().Text($"مدة النزول: {visit.DaysCount} يوم")
                            .FontSize(11).DirectionFromRightToLeft();
                    });
                });
            });
        }

        /// <summary>
        /// استخراج اسم السائق الفائز فقط من حقل SelectedDrivers
        /// </summary>
        private static string ExtractWinnerDriverName(string selectedDrivers)
        {
            if (string.IsNullOrEmpty(selectedDrivers))
            {
                return "لم يتم اختيار سائق بعد";
            }

            try
            {
                // تقسيم البيانات حسب الفاصل " | " أو السطر الجديد
                var driversData = selectedDrivers.Split(new string[] { " | ", "\n", "|" }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var driverData in driversData)
                {
                    // البحث عن السائق الذي يحتوي على "فائز"
                    if (driverData.Contains("🏆 فائز") || driverData.Contains("فائز"))
                    {
                        // استخراج اسم السائق (الجزء الأول قبل أول " - ")
                        var parts = driverData.Split(" - ");
                        if (parts.Length > 0)
                        {
                            return parts[0].Trim();
                        }
                    }
                }

                // إذا لم نجد سائق فائز، نأخذ أول سائق
                var firstDriverData = driversData.FirstOrDefault();
                if (!string.IsNullOrEmpty(firstDriverData))
                {
                    var parts = firstDriverData.Split(" - ");
                    if (parts.Length > 0)
                    {
                        return parts[0].Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استخراج اسم السائق الفائز: {ex.Message}");
            }

            return "لم يتم اختيار سائق بعد";
        }



        /// <summary>
        /// إنشاء قسم تفاصيل الإضافة/الخصم الاحترافي
        /// </summary>
        private static void CreateAdjustmentDetailsSection(IContainer container, string adjustmentType, int adjustmentDays, FieldVisit visit, decimal? customAmount = null)
        {
            container.Border(1).BorderColor("#000000").Padding(15).Column(col =>
            {
                // حساب القيم المطلوبة
                var originalAmount = GetDriverAmountFromDatabase(visit.VisitNumber);
                var totalDays = visit.DaysCount; // استخدام DaysCount من الزيارة مباشرة
                var dailyRate = totalDays > 0 ? originalAmount / totalDays : 0;

                // استخدام المبلغ المخصص إذا تم تمريره، وإلا احسب تلقائياً
                var adjustmentAmount = customAmount ?? (dailyRate * adjustmentDays);

                var totalDaysAfterAdjustment = adjustmentType == "إضافة" ? visit.DaysCount + adjustmentDays : visit.DaysCount - adjustmentDays;
                var finalAmount = adjustmentType == "إضافة" ? originalAmount + adjustmentAmount : originalAmount - adjustmentAmount;

                // الصف الأول: المبلغ المقدم، الأيام المضافة/المخصومة، مدة النزول بعد التعديل
                col.Item().Row(row =>
                {

                    // مساحة فاصلة
                    row.ConstantItem(10);

                    // الأجر اليومي
                    row.RelativeItem(1).Border(1).BorderColor("#000000").Padding(8).Column(cellCol =>
                    {
                        cellCol.Item().AlignCenter().Text("الأجر اليومي").FontSize(10).DirectionFromRightToLeft();
                        cellCol.Item().AlignCenter().Text($"{dailyRate:N0}").FontSize(11).Bold().DirectionFromRightToLeft();
                    });









                    // المبلغ المقدم
                    row.RelativeItem(1).Border(1).BorderColor("#000000").Padding(8).Column(cellCol =>
                    {
                        cellCol.Item().AlignCenter().Text("المبلغ المقدم").FontSize(10).DirectionFromRightToLeft();
                        cellCol.Item().AlignCenter().Text($"{originalAmount:N0}").FontSize(11).Bold().DirectionFromRightToLeft();
                    });

                    // مساحة فاصلة
                    row.ConstantItem(10);

                    // الأيام المضافة أو المخصومة
                    row.RelativeItem(1).Border(1).BorderColor("#000000").Padding(8).Column(cellCol =>
                    {
                        var daysLabel = adjustmentType == "إضافة" ? "الأيام المضافة" : "الأيام المخصومة";
                        cellCol.Item().AlignCenter().Text(daysLabel).FontSize(10).DirectionFromRightToLeft();
                        cellCol.Item().AlignCenter().Text($"{adjustmentDays} يوم").FontSize(11).Bold().DirectionFromRightToLeft();
                    });

                    // مساحة فاصلة
                    row.ConstantItem(10);

                    // مدة النزول بعد التعديل
                    row.RelativeItem(1).Border(1).BorderColor("#000000").Padding(8).Column(cellCol =>
                    {
                        var durationLabel = adjustmentType == "إضافة" ? "مدة النزول بعد الإضافة" : "مدة النزول بعد الخصم";
                        cellCol.Item().AlignCenter().Text(durationLabel).FontSize(10).DirectionFromRightToLeft();
                        cellCol.Item().AlignCenter().Text($"{totalDaysAfterAdjustment} أيام").FontSize(11).Bold().DirectionFromRightToLeft();
                    });
                });

                // مساحة بين الصفوف
                col.Item().Height(10);

                // الصف الثاني: المبلغ المستحق للسائق، الأجر اليومي، مبلغ الإضافة/الخصم
                col.Item().Row(row =>
                {
                    // المبلغ المستحق للسائق
                    row.RelativeItem(1).Border(1).BorderColor("#000000").Padding(8).Column(cellCol =>
                    {
                        cellCol.Item().AlignCenter().Text("المبلغ المستحق للسائق").FontSize(10).DirectionFromRightToLeft();
                        cellCol.Item().AlignCenter().Text($"{finalAmount:N0}").FontSize(11).Bold().DirectionFromRightToLeft();
                    });

                    

                    // مساحة فاصلة
                    row.ConstantItem(10);

                    // مبلغ الإضافة أو الخصم
                    row.RelativeItem(1).Border(1).BorderColor("#000000").Padding(8).Column(cellCol =>
                    {
                        var amountLabel = adjustmentType == "إضافة" ? "مبلغ الإضافة" : "مبلغ الخصم";
                        cellCol.Item().AlignCenter().Text(amountLabel).FontSize(10).DirectionFromRightToLeft();
                        cellCol.Item().AlignCenter().Text($"{adjustmentAmount:N0}").FontSize(11).Bold().DirectionFromRightToLeft();
                    });
                });
            });
        }

        /// <summary>
        /// استخراج مبلغ السائق من قاعدة البيانات
        /// </summary>
        private static decimal GetDriverAmountFromDatabase(string visitNumber)
        {
            try
            {
                using var context = new Data.ApplicationDbContext();

                // البحث عن السائق المقبول أولاً
                var acceptedQuote = context.DriverQuotes
                    .Where(q => q.VisitNumber == visitNumber && q.Status == Models.QuoteStatus.Accepted)
                    .FirstOrDefault();

                if (acceptedQuote != null)
                {
                    return acceptedQuote.QuotedPrice;
                }

                // إذا لم يوجد مقبول، نأخذ أول عرض متاح
                var firstQuote = context.DriverQuotes
                    .Where(q => q.VisitNumber == visitNumber)
                    .OrderBy(q => q.QuoteDate)
                    .FirstOrDefault();

                return firstQuote?.QuotedPrice ?? 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استخراج مبلغ السائق: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// استخراج مبلغ السائق من حقل SelectedDrivers (احتياطي)
        /// </summary>
        private static decimal ExtractDriverAmount(string selectedDrivers)
        {
            if (string.IsNullOrEmpty(selectedDrivers))
                return 0;

            try
            {
                // تقسيم النص للحصول على معلومات السائقين
                var drivers = selectedDrivers.Split('|');

                foreach (var driver in drivers)
                {
                    var parts = driver.Split('-');
                    if (parts.Length >= 3)
                    {
                        var status = parts[2].Trim();
                        if (status.Contains("مقبول") || status.Contains("فائز"))
                        {
                            var amountStr = parts[1].Trim();
                            // إزالة الفواصل والرموز
                            amountStr = amountStr.Replace(",", "").Replace("ريال", "").Replace("ر.س", "").Trim();
                            if (decimal.TryParse(amountStr, out decimal amount))
                            {
                                return amount;
                            }
                        }
                    }
                }

                // إذا لم نجد سائق فائز، نأخذ أول مبلغ متاح
                if (drivers.Length > 0)
                {
                    var firstDriver = drivers[0].Split('-');
                    if (firstDriver.Length >= 2)
                    {
                        var amountStr = firstDriver[1].Trim();
                        // إزالة الفواصل والرموز
                        amountStr = amountStr.Replace(",", "").Replace("ريال", "").Replace("ر.س", "").Trim();
                        if (decimal.TryParse(amountStr, out decimal amount))
                        {
                            return amount;
                        }
                    }
                }
            }
            catch
            {
                // في حالة الخطأ، نعيد قيمة افتراضية
            }

            return 0;
        }

        /// <summary>
        /// إنشاء قسم السبب
        /// </summary>
        private static void CreateReasonSection(IContainer container, string reason)
        {
            container.Border(1).BorderColor("#000000").Column(col =>
            {
                // عنوان القسم
                col.Item().Background("#E8E8E8").Padding(5).AlignCenter()
                    .Text("السبب").FontSize(11).Bold().DirectionFromRightToLeft();

                // محتوى السبب
                col.Item().Padding(5).MinHeight(40).AlignRight()
                    .Text(reason).FontSize(10).DirectionFromRightToLeft();
            });
        }

        /// <summary>
        /// البحث عن رقم التلفون للسائق من قاعدة البيانات
        /// </summary>
        private static string GetDriverPhoneNumber(string driverName)
        {
            if (string.IsNullOrEmpty(driverName) || driverName == "غير محدد" || driverName == "لم يتم اختيار سائق بعد")
                return "غير محدد";

            try
            {
                using (var context = new ApplicationDbContext())
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 البحث عن السائق: '{driverName}'");

                    // جلب جميع السائقين للفحص
                    var allDrivers = context.Drivers.ToList();
                    System.Diagnostics.Debug.WriteLine($"📊 عدد السائقين في قاعدة البيانات: {allDrivers.Count}");

                    foreach (var d in allDrivers.Take(5)) // عرض أول 5 سائقين للفحص
                    {
                        System.Diagnostics.Debug.WriteLine($"   - السائق: '{d.Name}' | التلفون: '{d.PhoneNumber}'");
                    }

                    // البحث بالاسم الكامل أولاً (تطابق تام)
                    var driver = allDrivers
                        .FirstOrDefault(d => d.Name.Trim().Equals(driverName.Trim(), StringComparison.OrdinalIgnoreCase));

                    if (driver == null)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ لم يوجد تطابق كامل، البحث بالاحتواء...");
                        // البحث بالاحتواء في كلا الاتجاهين
                        driver = allDrivers
                            .FirstOrDefault(d => d.Name.Contains(driverName, StringComparison.OrdinalIgnoreCase) ||
                                               driverName.Contains(d.Name, StringComparison.OrdinalIgnoreCase));
                    }

                    if (driver == null)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ لم يوجد تطابق بالاحتواء، البحث بالكلمات المفتاحية...");
                        // البحث بالكلمات المفتاحية
                        var keywords = driverName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                        foreach (var keyword in keywords)
                        {
                            if (keyword.Length > 2) // تجاهل الكلمات القصيرة
                            {
                                System.Diagnostics.Debug.WriteLine($"   🔍 البحث بالكلمة: '{keyword}'");
                                driver = allDrivers
                                    .FirstOrDefault(d => d.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase));
                                if (driver != null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"   ✅ وُجد تطابق بالكلمة '{keyword}': {driver.Name}");
                                    break;
                                }
                            }
                        }
                    }

                    if (driver != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على السائق: '{driver.Name}' | التلفون: '{driver.PhoneNumber}'");
                        if (!string.IsNullOrWhiteSpace(driver.PhoneNumber))
                        {
                            return driver.PhoneNumber;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("⚠️ السائق موجود لكن رقم التلفون فارغ");
                            return "غير محدد";
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على السائق: '{driverName}'");
                        return "غير محدد";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن رقم التلفون للسائق '{driverName}': {ex.Message}");
                return "غير محدد";
            }
        }

        /// <summary>
        /// إنشاء قسم التوقيعات
        /// </summary>
        private static void CreateSignaturesSection(IContainer container, FieldVisit visit)
        {
            container.PaddingTop(20).Row(row =>
            {


                // يعتمد مدير الفرع
                row.RelativeItem().Column(col =>
                {
                    col.Item().AlignCenter().Text("يعتمد مدير الفرع")
                        .FontSize(10).Bold().DirectionFromRightToLeft();
                    col.Item().PaddingTop(30);

                    // اسم مدير الفرع
                    col.Item().PaddingTop(5).AlignCenter().Text("م/محمد محمد الديلمي")
                        .FontSize(9).DirectionFromRightToLeft();
                });




                row.ConstantItem(50);

                // القائم بالمهمة
                row.RelativeItem().Column(col =>
                {
                    col.Item().AlignCenter().Text("القائم بالمهمة")
                        .FontSize(10).Bold().DirectionFromRightToLeft();
                    col.Item().PaddingTop(30);

                    // اسم القائم بالمهمة
                    var conductorName = visit.Visitors?.FirstOrDefault()?.Name ?? "غير محدد";
                    col.Item().PaddingTop(5).AlignCenter().Text(conductorName)
                        .FontSize(9).DirectionFromRightToLeft();
                });





                row.ConstantItem(50);

                // توقيع السائق
                row.RelativeItem().Column(col =>
                {
                    col.Item().AlignCenter().Text("السائق")
                        .FontSize(10).Bold().DirectionFromRightToLeft();
                    col.Item().PaddingTop(30);

                    // اسم السائق
                    var winnerDriverName = ExtractWinnerDriverName(visit.SelectedDrivers);
                    col.Item().PaddingTop(5).AlignCenter().Text(winnerDriverName)
                        .FontSize(9).DirectionFromRightToLeft();
                });







            });
        }
    }
}
